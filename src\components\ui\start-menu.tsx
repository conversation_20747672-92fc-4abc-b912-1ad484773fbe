import { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { getAvatarUrl } from '@/utils/avatarUtils';
import { 
  Copy, 
  Check, 
  User, 
  LogOut, 
  Home, 
  Tag, 
  ShoppingBag, 
  Settings, 
  Users, 
  Bell, 
  Heart, 
  Share2,
  X,
  LogIn,
  ArrowUpRight,
  Compass,
  Bookmark,
  BarChart4,
  Crown,
  Building2,
  HelpCircle,
  Store,
  LayoutGrid,
  Link as LinkIcon,
  WalletCards,
  BookOpen,
  CheckCircle,
  Circle,
  Play,
  ChevronRight,
  Sparkles,
  Menu
} from 'lucide-react';
import { Twitter, Facebook, Linkedin } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import { toast } from 'sonner';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useUser } from '@/hooks/useUsers';
import { useAnalytics } from '@/hooks/useAnalytics';
import { useIsMobile } from '@/hooks/use-mobile';

// Reusable Share Profile Popover Component
const ShareProfilePopover = ({ profile, user, onClose, navigate, handleCopyProfileLink, handleSocialShare }) => {
  return (
    <PopoverContent 
      className="w-[90vw] max-w-[280px] sm:w-64 p-0 overflow-hidden border-none shadow-2xl rounded-xl z-[51]"
      sideOffset={10}
      align="center"
      side="top"
      collisionPadding={{ top: 20, right: 20, bottom: 100, left: 20 }}
      avoidCollisions={true}
      sticky="partial"
    >
      <div className="flex flex-col">
        {/* Header with particle animation background */}
        <div className="relative overflow-hidden bg-gradient-to-br from-indigo-600 via-blue-600 to-violet-600 p-2">
          {/* Decorative circles */}
          <div className="absolute top-0 left-0 w-20 h-20 rounded-full bg-white/5 -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute bottom-0 right-0 w-32 h-32 rounded-full bg-white/5 translate-x-1/3 translate-y-1/3"></div>
          
          <h3 className="relative text-base font-bold text-white mb-1">Share Your Profile</h3>
        </div>
        
        {/* Profile quick view */}
        <div className="p-2 bg-gradient-to-br from-slate-50 to-white">
          <div className="flex items-center mb-1">
            <Avatar className="h-10 w-10 border-2 border-indigo-100 shadow-sm">
              <AvatarImage src={getAvatarUrl(profile?.avatar_url, profile?.id, user?.email)} />
              <AvatarFallback className="bg-indigo-100 text-indigo-700">
                {user?.email?.substring(0, 2).toUpperCase() || 'U'}
              </AvatarFallback>
            </Avatar>
            <div className="ml-3">
              <div className="text-sm font-medium text-gray-900 flex items-center">
                {profile?.full_name || 'Your Profile'}
                <div className="ml-2 h-2 w-2 rounded-full bg-green-500"></div>
              </div>
              <div className="text-xs text-gray-500 flex items-center">
                @{profile?.username || 'username'}
                {profile?.is_verified && (
                  <span className="inline-flex items-center justify-center bg-blue-500 rounded-full w-3 h-3 ml-1" title="Verified Account">
                    <Check className="w-1.5 h-1.5 text-white" strokeWidth={3} />
                  </span>
                )}
              </div>
            </div>
          </div>
          
          {/* URL Copy with improved styling */}
          <div className="relative mt-1 flex mb-1 group">
            <div className="flex-grow flex items-center bg-gradient-to-r from-slate-100 to-slate-50 border border-slate-200 rounded-l-lg px-3 py-1.5 text-xs text-slate-600 font-mono overflow-hidden whitespace-nowrap relative">
              <LinkIcon className="h-3.5 w-3.5 text-slate-400 mr-2 shrink-0" />
              <span className="overflow-ellipsis overflow-hidden">
                {window.location.origin}/{profile?.username || 'username'}
              </span>
              <div className="absolute inset-y-0 right-0 w-6 bg-gradient-to-l from-slate-50 to-transparent pointer-events-none"></div>
            </div>
            <button 
              onClick={handleCopyProfileLink}
              className="shrink-0 bg-gradient-to-r from-indigo-500 to-blue-500 px-3 py-1.5 rounded-r-lg shadow-sm hover:shadow-md hover:from-indigo-600 hover:to-blue-600 transition-all duration-200 transform hover:-translate-y-0.5 active:translate-y-0 flex items-center justify-center"
            >
              <Copy className="h-4 w-4 text-white group-hover:scale-110 transition-transform" />
            </button>
          </div>

          {/* Social sharing options */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-1 p-2">
            {/* Button with 3D effect */}
            {[
              { icon: <Twitter className="h-4 w-4" />, color: 'bg-[#1DA1F2]', label: 'Tweet', platform: 'twitter' },
              { icon: <Facebook className="h-4 w-4" />, color: 'bg-[#4267B2]', label: 'Share', platform: 'facebook' },
              { icon: <Linkedin className="h-4 w-4" />, color: 'bg-[#0077B5]', label: 'Post', platform: 'linkedin' },
              { icon: <Copy className="h-4 w-4" />, color: 'bg-gray-700', label: 'Copy', platform: 'copy' }
            ].map((item, index) => (
              <button 
                key={index}
                onClick={() => handleSocialShare(item.platform)}
                className={`${item.color} text-white rounded-lg p-2 shadow-sm hover:shadow-md transform hover:-translate-y-0.5 transition-all flex flex-col items-center justify-center gap-1`}
              >
                {item.icon}
                <span className="text-[10px]">{item.label}</span>
              </button>
            ))}
          </div>
        </div>
        
        {/* Footer */}
        <div className="p-1 bg-gray-50 border-t border-gray-100 flex justify-between items-center">
          <span className="text-xs text-gray-500">More options</span>
          <Link 
            to={`/profile/preview`}
            className="text-xs text-indigo-600 font-medium flex items-center gap-1 hover:text-indigo-800 transition-colors"
            onClick={(e) => {
              e.preventDefault();
              onClose();
              navigate(`/profile/preview`);
            }}
          >
            Advanced Sharing
            <ArrowUpRight className="h-3 w-3" />
          </Link>
        </div>
      </div>
    </PopoverContent>
  );
};

// Reusable Navigation Link Component with proper navigation
const NavigationLink = ({ to, onClick, children, className }) => {
  return (
    <Link
      to={to}
      className={className}
      onClick={() => {
        // Don't prevent default - let Link handle navigation properly
        if (onClick) onClick();
      }}
    >
      {children}
    </Link>
  );
};

// Compact Navigation Card Component
const NavigationCard = ({ to, icon, title, description, color, onClick }) => {
  return (
    <NavigationLink
      to={to}
      className="group relative p-2.5 rounded-lg transition-all duration-300 hover:bg-gray-50 hover:scale-105 hover:shadow-md cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center gap-2.5">
        <div className={`w-8 h-8 rounded-lg flex items-center justify-center bg-gradient-to-br ${icon.bgColor}
          text-white group-hover:scale-110 group-hover:shadow-lg transition-all duration-300`}>
          <div className="scale-75">
            {icon.element}
          </div>
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-xs font-medium text-gray-900 truncate group-hover:text-primary-main transition-colors duration-300">
            {title}
          </h4>
          <p className="text-[10px] text-gray-600 truncate group-hover:text-gray-700 transition-colors duration-300">
            {description}
          </p>
        </div>
      </div>
    </NavigationLink>
  );
};

// Compact Quick Access Icon Component
const QuickAccessIcon = ({ to, icon, label, color, onClick, isSignOut = false }) => {
  // For sign-out actions, use a button instead of a Link to avoid navigation conflicts
  if (isSignOut) {
    return (
      <button
        onClick={onClick}
        className="group flex flex-col items-center p-2 rounded-lg transition-all duration-300 hover:bg-gray-50 hover:scale-105 cursor-pointer"
      >
        <div className={`w-7 h-7 mb-1.5 rounded-lg bg-${color}-100 flex items-center justify-center
          group-hover:scale-110 group-hover:shadow-md transition-all duration-300`}>
          <div className="scale-75">
            {icon}
          </div>
        </div>
        <span className="text-[10px] font-medium text-gray-700 text-center leading-tight group-hover:text-gray-900 transition-colors duration-300">
          {label}
        </span>
      </button>
    );
  }

  // For regular navigation, use NavigationLink
  return (
    <NavigationLink
      to={to}
      onClick={onClick}
      className="group flex flex-col items-center p-2 rounded-lg transition-all duration-300 hover:bg-gray-50 hover:scale-105 cursor-pointer"
    >
      <div className={`w-7 h-7 mb-1.5 rounded-lg bg-${color}-100 flex items-center justify-center
        group-hover:scale-110 group-hover:shadow-md transition-all duration-300`}>
        <div className="scale-75">
          {icon}
        </div>
      </div>
      <span className="text-[10px] font-medium text-gray-700 text-center leading-tight group-hover:text-gray-900 transition-colors duration-300">
        {label}
      </span>
    </NavigationLink>
  );
};

// Simple Menu Section Component
const MenuSection = ({ title, children }) => {
  return (
    <div>
      {title && (
        <div className="mb-2">
          <h3 className="text-xs font-semibold text-slate-600 uppercase tracking-wide">{title}</h3>
        </div>
      )}
      {children}
    </div>
  );
};

// Clean Sidebar Navigation Link
const SidebarNavLink = ({ to, icon, label, iconBgColor, onClick }) => {
  return (
    <NavigationLink
      to={to}
      className="group flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-white/80 w-full text-left
        text-slate-700 hover:text-blue-600 transition-all duration-200"
      onClick={onClick}
    >
      <div className={`w-7 h-7 flex items-center justify-center ${iconBgColor} rounded-lg
        group-hover:scale-105 transition-transform duration-200`}>
        {icon}
      </div>
      <span className="text-sm font-medium">
        {label}
      </span>
    </NavigationLink>
  );
};

// Navigation Data for reuse
const navigationData = {
  mainNavigation: [
    {
      to: "/trending",
      icon: { element: <BarChart4 className="h-5 w-5" />, bgColor: "from-orange-500 to-red-600" },
      title: "Trending",
      description: "Popular right now",
      color: "from-orange-50 to-red-50",
    },
    {
      to: "/categories",
      icon: { element: <LayoutGrid className="h-5 w-5" />, bgColor: "from-cyan-500 to-blue-600" },
      title: "Categories",
      description: "Browse by type",
      color: "from-cyan-50 to-blue-50",
    },
    {
      to: "/explore",
      icon: { element: <Compass className="h-5 w-5" />, bgColor: "from-purple-500 to-pink-600" },
      title: "Explore",
      description: "Discover new deals",
      color: "from-purple-50 to-pink-50",
    },
    {
      to: "/saved",
      icon: { element: <Bookmark className="h-5 w-5" />, bgColor: "from-pink-500 to-rose-600" },
      title: "Saved Coupons",
      description: "Your saved deals",
      color: "from-pink-50 to-rose-50",
    },
  ],
  forYou: [
    {
      to: "/premium",
      icon: { element: <Crown className="h-5 w-5" />, bgColor: "from-amber-500 to-yellow-600" },
      title: "Premium",
      description: "Exclusive benefits",
      color: "from-amber-50 to-yellow-50",
    },
  ],
  quickAccess: [
    { to: "/coupons", icon: <Store className="h-5 w-5 text-amber-600" />, label: "Coupons", color: "amber" },
    { to: "/brands", icon: <Building2 className="h-5 w-5 text-blue-600" />, label: "Brands", color: "blue" },
    { to: "/flash-sales", icon: <Bell className="h-5 w-5 text-purple-600" />, label: "Flash ", color: "purple" },
    { to: "/premium", icon: <Tag className="h-5 w-5 text-green-600" />, label: "Premium ", color: "green" },
  ],
  sidebar: [
    { to: "/home", icon: <Home className="h-3.5 w-3.5" />, label: "Home", iconBgColor: "bg-blue-500/10 text-blue-600" },
    { to: "/profile", icon: <User className="h-3.5 w-3.5" />, label: "Profile", iconBgColor: "bg-indigo-500/10 text-indigo-600" },
    { to: "/blog", icon: <BookOpen className="h-3.5 w-3.5" />, label: "Blog", iconBgColor: "bg-green-500/10 text-green-600" },
    { to: "/settings", icon: <Settings className="h-3.5 w-3.5" />, label: "Settings", iconBgColor: "bg-purple-500/10 text-purple-600" },
    { to: "/help", icon: <HelpCircle className="h-3.5 w-3.5" />, label: "Help", iconBgColor: "bg-purple-500/10 text-purple-600" },
  ],
  accountQuickAccess: [
    { to: "/home", icon: <Home className="h-5 w-5 text-blue-600" />, label: "Home", color: "blue" },
    { to: "/profile", icon: <User className="h-5 w-5 text-indigo-600" />, label: "Profile", color: "indigo" },
    { to: "/settings", icon: <Settings className="h-5 w-5 text-purple-600" />, label: "Settings", color: "purple" },
    { to: "#", icon: <LogOut className="h-5 w-5 text-red-600" />, label: "Logout", color: "red", isSignOut: true },
  ],
};



interface StartMenuProps {
  isOpen: boolean;
  onClose: () => void;
}

const StartMenu = ({ isOpen, onClose }: StartMenuProps) => {
  const { user, signOut, profile } = useAuth();
  const isMobile = useIsMobile();
  const menuRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const location = useLocation();
  
  // Fix conditional hook call - always call useUser but use 'enabled' property
  const { data: profileData } = useUser(profile?.username || '', {
    enabled: !!profile?.username
  });
  
  const { trackProfileShare } = useAnalytics();
  
  // Get the username for sharing - default to email if profile username not available
  const usernameForShare = profile?.username || (user?.email ? user.email.split('@')[0] : 'user');

  const [isSharePopoverOpen, setIsSharePopoverOpen] = useState(false);

  const handleCopyProfileLink = async () => {
    try {
      const link = `${window.location.origin}/${profile?.username || 'username'}`;
      await navigator.clipboard.writeText(link);
      toast.success('Profile link copied to clipboard!');
      
      // Track the profile share
      if (profile?.id) {
        trackProfileShare(profile.id, 'copy');
      }
    } catch (error) {
      toast.error('Failed to copy link');
    }
  };

  const handleSocialShare = async (platform: string) => {
    const profileUrl = `${window.location.origin}/${profile?.username || 'username'}`;
    const message = `Check out my profile and coupon deals!`;

    // Different share URLs based on platform
    let shareUrl = '';
    switch (platform) {
      case 'twitter':
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(message)}&url=${encodeURIComponent(profileUrl)}`;
        break;
      case 'facebook':
        shareUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(profileUrl)}`;
        break;
      case 'linkedin':
        shareUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(profileUrl)}`;
        break;
      default:
        await handleCopyProfileLink();
        return;
    }

    // Open share window
    if (shareUrl) {
      window.open(shareUrl, '_blank');
      toast.success(`Shared via ${platform}!`);
      
      // Track the profile share
      if (profile?.id) {
        trackProfileShare(profile.id, platform);
      }
    }
  };

  // Close menu on location change - more reliable approach
  useEffect(() => {
    // Only run if the menu is currently open
    if (isOpen) {
      // Store the current location pathname for comparison
      const currentPathname = location.pathname;
      
      // Create a navigation event listener
      const handleNavigation = () => {
        // If location changed while menu was open, close the menu
        if (location.pathname !== currentPathname) {
          onClose();
        }
      };
      
      // Listen for navigation events
      window.addEventListener('popstate', handleNavigation);
      
      // Clean up the listener when component unmounts or menu closes
      return () => {
        window.removeEventListener('popstate', handleNavigation);
      };
    }
  }, [isOpen, location.pathname, onClose]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  // Helper function for handling Link clicks - just close menu, let Link handle navigation
  const handleLinkClick = () => {
    // Close the menu - Link component will handle navigation properly
    onClose();
  };

  // Handle navigation to auth page
  const handleSignIn = () => {
    navigate('/landing#username-form');
    onClose();
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      console.log('Start menu: Initiating sign out...');

      // Close the menu first to provide immediate feedback
      onClose();

      // Then call the signOut function (which handles navigation)
      await signOut();

      console.log('Start menu: Sign out completed');
    } catch (error) {
      console.error('Start menu: Error during sign out:', error);
      // Ensure menu is closed and navigate even if signOut fails
      onClose();
      navigate('/auth');
    }
  };

  if (!isOpen) return null;

  // Common ShareProfilePopover props
  const shareProfilePopoverProps = {
    profile,
    user,
    onClose,
    navigate,
    handleCopyProfileLink,
    handleSocialShare,
  };

  return (
    <>
      {/* Backdrop - semi-transparent overlay with blur that doesn't affect taskbar */}
      <div
        className={`fixed inset-0 bg-black/20 backdrop-blur-md z-50 transition-opacity duration-300 ${
          isOpen ? 'opacity-100' : 'opacity-0 pointer-events-none'
        }`}
        onClick={(e) => {
          // Only close if clicking on the backdrop itself, not on any children
          if (e.target === e.currentTarget) {
            onClose();
          }
        }}
      />

      {/* Clean Professional Start Menu */}
      <div
        ref={menuRef}
        className={`
          fixed z-[51] bg-white shadow-lg overflow-hidden
          border border-gray-200 rounded-xl
          transition-all duration-300 ease-out
          ${isMobile
            ? 'bottom-24 left-4 right-4 h-[50vh]'
            : 'bottom-[8rem] left-1/2 transform -translate-x-1/2 w-[580px] h-[420px] max-w-[95vw]'
          }
          ${isOpen
            ? 'translate-y-0 opacity-100 scale-100'
            : 'translate-y-8 opacity-0 scale-95 pointer-events-none'
          }
        `}
      >
        {/* Conditional inner backdrop for blurring start menu content when share popover is open */}
        {isSharePopoverOpen && (
          <div
            className="absolute inset-0 bg-white/30 backdrop-blur-sm z-50 rounded-xl"
            onClick={() => setIsSharePopoverOpen(false)}
          />
        )}

        {/* Clean Professional Header */}
        <div className="flex items-center p-4 border-b border-gray-200 bg-gray-50">

          {/* User Profile Section - Flexible with proper overflow handling */}
          <div className="flex items-center gap-3 relative z-10 flex-1 min-w-0">
            {user ? (
              <>
                <Link to="/profile" className="relative group cursor-pointer flex-shrink-0" onClick={onClose}>
                  <Avatar className="h-10 w-10 ring-2 ring-offset-2 ring-slate-100/50 transition-all group-hover:ring-blue-200">
                  <AvatarImage src={getAvatarUrl(profile?.avatar_url, profile?.id, user?.email)} />
                    <AvatarFallback className="bg-gradient-to-br from-primary-main to-tertiary-main text-white">
                      {user.email ? user.email.charAt(0).toUpperCase() : '?'}
                    </AvatarFallback>
                </Avatar>
                  <span className="absolute bottom-0 right-0 h-3 w-3 bg-primary-main rounded-full ring-2 ring-white"></span>
                </Link>

                {/* User Info - Flexible with text truncation */}
                <Link to="/profile" className="cursor-pointer flex-1 min-w-0" onClick={onClose}>
                  <p className="font-semibold text-gray-800 flex items-center truncate">
                    <span className="truncate">
                      {profile?.username || user?.email?.split('@')[0] || 'User'}
                    </span>
                    {profile?.is_verified && (
                      <span className="inline-flex items-center justify-center bg-tertiary-main rounded-full w-3.5 h-3.5 ml-1 flex-shrink-0" title="Verified Account">
                        <Check className="w-2 h-2 text-white" strokeWidth={3} />
                      </span>
                    )}
                  </p>
                  <p className="text-xs text-gray-600 truncate">{user?.email || ''}</p>
                </Link>

                {/* Share profile button - Fixed position, won't overflow */}
                <div className="flex-shrink-0">
                    <Popover open={isSharePopoverOpen} onOpenChange={setIsSharePopoverOpen}>
                      <PopoverTrigger asChild>
                      <button className={`
                        relative group overflow-hidden flex items-center gap-1.5
                        ${isMobile ? 'px-2 py-1' : 'px-3 py-1.5'}
                        rounded-xl
                        bg-gradient-to-r from-primary-main via-accent-main to-tertiary-main
                        hover:from-primary-light hover:via-accent-light hover:to-tertiary-light
                        transition-all duration-300 transform
                        border border-white/20
                        hover:border-white/40
                        shadow-[0_0_12px_rgba(14,167,107,0.2)]
                        hover:shadow-[0_0_20px_rgba(14,167,107,0.3)]`}>

                        {/* Modern glass container for icon */}
                        <div className={`
                          relative z-10 rounded-lg
                          ${isMobile ? 'w-4 h-4' : 'w-6 h-6'}
                          bg-gradient-to-br from-white/20 to-white/5 backdrop-blur-sm
                          flex items-center justify-center
                          before:absolute before:inset-0 before:rounded-lg
                          before:bg-gradient-to-br before:from-white/20 before:to-transparent
                          before:opacity-0 group-hover:before:opacity-100
                          before:transition-opacity before:duration-300`}>
                          <Share2 className={`${isMobile ? 'h-2.5 w-2.5' : 'h-3.5 w-3.5'} text-white transform
                            group-hover:scale-110 transition-transform`} />

                          {/* Subtle highlight */}
                          <div className="absolute -inset-[0.5px] rounded-lg bg-gradient-to-br from-white/50 to-white/0 opacity-20"></div>
                          </div>

                        {/* Text with modern styling */}
                        <div className="relative z-10 flex flex-col">
                          <span className={`${isMobile ? 'text-[10px]' : 'text-xs'} font-semibold text-white tracking-wide
                            group-hover:text-white/90 whitespace-nowrap`}>
                            {isMobile ? 'Share' : 'Share Profile'}
                          </span>
                          {!isMobile && (
                            <span className="text-[9px] text-white/70 font-medium tracking-wider
                              group-hover:text-white/80">
                              Expand Your Network
                            </span>
                          )}
                        </div>

                        {/* Modern decorative elements - Only show on desktop */}
                        {!isMobile && (
                          <div className="relative z-10 ml-auto flex items-center gap-1">
                            <div className="w-3 h-3 rounded-full bg-gradient-to-r from-violet-500/20 to-indigo-500/20
                              flex items-center justify-center">
                              <div className="w-0.5 h-0.5 rounded-full bg-white/60
                                group-hover:bg-white/80 transition-colors"></div>
                            </div>
                          </div>
                        )}

                        {/* Professional shine effect */}
                        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent
                          translate-x-[-200%] group-hover:translate-x-[200%]
                          transition-transform duration-700 ease-out"></div>

                        {/* Subtle background pattern */}
                        <div className="absolute inset-0 opacity-20">
                          <div className="absolute inset-0 bg-[radial-gradient(circle_at_110%_90%,rgba(139,92,246,0.5),transparent_40%)]"></div>
                          <div className="absolute inset-0 bg-[radial-gradient(circle_at_-10%_10%,rgba(99,102,241,0.5),transparent_30%)]"></div>
                        </div>
                        </button>
                      </PopoverTrigger>
                      <ShareProfilePopover {...shareProfilePopoverProps} />
                    </Popover>
                  </div>
              </>
            ) : (
              <div className="flex items-center gap-2 flex-1 min-w-0">
                <div className="w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center text-gray-400 flex-shrink-0">
                  <User className="h-5 w-5" />
                </div>
                <span className="text-gray-600 font-medium truncate">Guest User</span>
              </div>
            )}
          </div>

          {/* Close Button - Fixed position */}
          <Button variant="ghost" size="icon" className="relative z-10 rounded-full hover:bg-gray-100/70 flex-shrink-0 ml-3" onClick={onClose}>
            <X className="h-4 w-4 text-gray-500" />
          </Button>
        </div>

        <div className={`${isMobile ? 'flex flex-col h-[calc(100%-72px)]' : 'flex h-[calc(100%-72px)]'}`}>
          {/* Clean Left Sidebar - Only for desktop */}
          {!isMobile && (
            <div className="w-48 border-r border-gray-200 p-4 flex flex-col bg-gray-50">
                <div className="space-y-1">
                  {/* Render sidebar links from data */}
                  {navigationData.sidebar.map((item, index) => (
                    user || index < 2 ? (
                      <SidebarNavLink
                        key={index}
                        to={item.to}
                        icon={item.icon}
                        label={item.label}
                        iconBgColor={item.iconBgColor}
                        onClick={handleLinkClick}
                      />
                    ) : null
                  ))}

                  {user ? (
                    // Removed share profile button from here since it's now in the header
                    <div></div>
                  ) : (
                    <button
                      className="w-full mt-3 bg-gradient-to-r from-primary-main to-tertiary-main hover:from-primary-light hover:to-tertiary-light text-white rounded-lg px-3 py-2
                        flex items-center justify-center gap-2 transition-all duration-200 shadow-lg"
                      onClick={handleSignIn}
                    >
                      <LogIn className="h-4 w-4" />
                      <span className="font-medium">Sign Up</span>
                    </button>
                  )}
                </div>

                {user && (
                  <div className="mt-auto">
                    <div className="h-px bg-gray-200/40 mb-3"></div>
                    <button
                      className="group flex items-center gap-2 px-3 py-2 rounded-lg hover:bg-red-500/10 w-full text-left
                        text-red-600 hover:text-red-700 transition-colors duration-200"
                      onClick={handleSignOut}
                    >
                      <div className="w-7 h-7 flex items-center justify-center bg-red-500/10 text-red-600 rounded-lg
                        group-hover:bg-red-500/20 transition-colors duration-200">
                        <LogOut className="h-3.5 w-3.5" />
                      </div>
                      <span className="text-sm font-medium">Sign Out</span>
                    </button>
                  </div>
                )}
            </div>
          )}

          {/* Main Content */}
          <div className={`flex-1 ${isMobile ? 'overflow-auto' : 'overflow-hidden'}`}>
            {isMobile ? (
              // Compact Mobile layout - no footer needed
              <div className="p-3 space-y-3">
                  {/* Main Navigation */}
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {navigationData.mainNavigation.slice(0, 4).map((item, index) => (
                    <NavigationCard
                        key={index}
                        to={item.to}
                        icon={item.icon}
                        title={item.title}
                        description={item.description}
                        color={item.color}
                        onClick={handleLinkClick}
                      />
                    ))}
                  </div>



                  {/* Quick access section */}
                  <div className="grid grid-cols-4 gap-1.5 mb-4">
                    {navigationData.quickAccess.map((item, index) => (
                    <QuickAccessIcon
                        key={index}
                        to={item.to}
                        icon={item.icon}
                        label={item.label}
                        color={item.color}
                        onClick={handleLinkClick}
                      />
                    ))}
                  </div>

                  {/* Account Navigation */}
                  <div className="grid grid-cols-4 gap-1.5 mb-4">
                  {user ? (
                    navigationData.accountQuickAccess.map((item, index) => (
                    <QuickAccessIcon
                        key={index}
                        to={item.to}
                        icon={item.icon}
                        label={item.label}
                        color={item.color}
                        onClick={item.isSignOut ? handleSignOut : handleLinkClick}
                        isSignOut={item.isSignOut}
                      />
                    ))
                  ) : (
                    <>
                    <QuickAccessIcon
                      to="/home"
                      icon={<Home className="h-5 w-5 text-blue-600" />}
                      label="Home"
                      color="blue"
                      onClick={handleLinkClick}
                    />
                    <QuickAccessIcon
                      to="/auth?mode=signup"
                      icon={<LogIn className="h-5 w-5 text-indigo-600" />}
                      label="Sign Up"
                      color="indigo"
                      onClick={handleSignIn}
                    />
                    </>
                  )}
                  </div>

                  {/* Quick Stats - Only show for logged in users and hide on mobile */}
                  {user && !isMobile && (
                    <div className="p-3 mb-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center gap-1">
                            <Bookmark className="h-3 w-3 text-primary-main" />
                            <span className="text-xs font-medium text-gray-700">12 Saved</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Tag className="h-3 w-3 text-secondary-main" />
                            <span className="text-xs font-medium text-gray-700">$45 Saved</span>
                          </div>
                        </div>
                        <Link
                          to="/dashboard"
                          onClick={onClose}
                          className="text-[10px] text-primary-main hover:text-primary-dark font-medium"
                        >
                          View All →
                        </Link>
                      </div>
                    </div>
                  )}
              </div>
            ) : (
              // Compact Desktop view with flex column for footer positioning
              <div className="p-3 h-full flex flex-col">
                {/* Main content sections */}
                <div className="space-y-3 flex-1">
                  {/* Featured Navigation */}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    {navigationData.mainNavigation.slice(0, 2).map((item, index) => (
                    <NavigationCard
                        key={index}
                        to={item.to}
                        icon={item.icon}
                        title={item.title}
                        description={item.description}
                        color={item.color}
                        onClick={handleLinkClick}
                      />
                    ))}
                  </div>

                  {/* Explore Section */}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    {navigationData.mainNavigation.slice(2, 4).map((item, index) => (
                    <NavigationCard
                        key={index}
                        to={item.to}
                        icon={item.icon}
                        title={item.title}
                        description={item.description}
                        color={item.color}
                        onClick={handleLinkClick}
                      />
                    ))}
                  </div>



                  {/* Quick Actions */}
                  <div className="grid grid-cols-4 gap-1.5 mb-3">
                    {navigationData.quickAccess.map((item, index) => (
                    <QuickAccessIcon
                        key={index}
                        to={item.to}
                        icon={item.icon}
                        label={item.label}
                        color={item.color}
                        onClick={handleLinkClick}
                      />
                    ))}
                  </div>
                </div>

                {/* Trending Deals - For non-logged in users */}
                {!user && (
                  <div className="p-3 mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-2">
                        <BarChart4 className="h-4 w-4 text-secondary-main" />
                        <span className="text-xs font-semibold text-gray-700">Hot Deals Today</span>
                      </div>
                      <span className="text-[10px] text-secondary-main bg-secondary-main/10 px-2 py-0.5 rounded-full">Live</span>
                    </div>
                    <div className="text-[10px] text-gray-600 space-y-1">
                      <div className="flex justify-between">
                        <span>Amazon - Up to 70% off</span>
                        <span className="text-primary-main font-medium">Save $50</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Nike - Flash Sale</span>
                        <span className="text-primary-main font-medium">Save $25</span>
                      </div>
                    </div>
                  </div>
                )}

                {/* App Info & Links - Footer - Desktop Only */}
                <div className="p-3 mt-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      <img
                        src="/logos/logo.png"
                        alt="PromoCoupons Logo"
                        className="w-6 h-6 rounded-md object-contain"
                        onError={(e) => {
                          // Fallback to text logo if image fails to load
                          const target = e.target as HTMLImageElement;
                          target.style.display = 'none';
                          const fallback = target.nextElementSibling as HTMLElement;
                          if (fallback) fallback.style.display = 'flex';
                        }}
                      />
                      <div className="w-6 h-6 bg-gradient-to-br from-primary-main to-tertiary-main rounded-md items-center justify-center hidden">
                        <span className="text-white text-xs font-bold">P</span>
                      </div>
                      <span className="text-xs font-semibold text-gray-700">PromoCoupons</span>
                    </div>
                    <span className="text-[10px] text-gray-500 bg-gray-200/50 px-2 py-0.5 rounded-full">v2.1.0</span>
                  </div>

                  <div className="flex items-center justify-between text-[10px] text-gray-500">
                    <div className="flex items-center gap-3">
                      <Link
                        to="/privacy"
                        onClick={onClose}
                        className="hover:text-primary-main transition-colors"
                      >
                        Privacy
                      </Link>
                      <Link
                        to="/terms"
                        onClick={onClose}
                        className="hover:text-primary-main transition-colors"
                      >
                        Terms
                      </Link>
                      <Link
                        to="/help"
                        onClick={onClose}
                        className="hover:text-primary-main transition-colors"
                      >
                        Help
                      </Link>
                    </div>
                    <span>© 2024 PromoCoupons</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </>
  );
};

export default StartMenu; 