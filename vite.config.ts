import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";

// https://vitejs.dev/config/
export default defineConfig({
  base: "/",
  server: {
    port: 8080,
    strictPort: false,
    hmr: {
      overlay: true,
      clientPort: 8080
    },
    fs: {
      strict: false,
      allow: [".."],
    },
    open: true,
    // Configure static file serving for SEO files
    middlewareMode: false,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  },
  plugins: [react()],
  resolve: {
    alias: {
      "@": "/src",
    },
  },
  css: {
    devSourcemap: true,
  },
  // Suppress source map warnings for dependencies
  logLevel: 'warn',
  // Ensure static files are properly served
  publicDir: 'public',
  build: {
    sourcemap: true,
    outDir: "dist",
    rollupOptions: {
      output: {
        manualChunks: {
          react: ['react', 'react-dom', 'react-router-dom'],
          ui: ['@radix-ui/react-accordion', '@radix-ui/react-avatar', '@radix-ui/react-dialog', '@radix-ui/react-label', '@radix-ui/react-popover', '@radix-ui/react-progress', '@radix-ui/react-select', '@radix-ui/react-slot', '@radix-ui/react-switch', '@radix-ui/react-tabs', '@radix-ui/react-toast', '@radix-ui/react-tooltip'],
          vendor: ['@supabase/supabase-js', '@tanstack/react-query', 'framer-motion', 'sonner', 'lucide-react']
        }
      },
      // Suppress source map warnings for external dependencies
      onwarn(warning, warn) {
        if (warning.code === 'SOURCEMAP_ERROR') return;
        warn(warning);
      }
    },
    chunkSizeWarningLimit: 1000,
  },
  optimizeDeps: {
    force: false, // Changed from true to false for faster startup
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      '@supabase/supabase-js',
      '@tanstack/react-query',
      'framer-motion',
      'sonner',
      'lucide-react',
      '@radix-ui/react-accordion',
      '@radix-ui/react-avatar',
      '@radix-ui/react-dialog',
      '@radix-ui/react-label',
      '@radix-ui/react-popover',
      '@radix-ui/react-select',
      '@radix-ui/react-switch',
      '@radix-ui/react-tabs',
      '@radix-ui/react-toast',
      '@radix-ui/react-tooltip'
    ],
    exclude: ['@vite/client', '@vite/env']
  },
  // Additional configuration to suppress source map warnings
  esbuild: {
    logOverride: { 'this-is-undefined-in-esm': 'silent' }
  },
  // Performance optimizations for faster startup
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV !== 'production')
  }
});
