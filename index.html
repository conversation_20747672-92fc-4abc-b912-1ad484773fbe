<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="google-site-verification" content="IDzBi05j6oCk4k4ivulnB4iWpFNAvP_go43FsEitelM" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, shrink-to-fit=no" />
    <title>CouponLink - Influencer Coupon Management Platform</title>
    <meta name="description" content="Find and share verified coupon codes, promo offers, and discounts from your favorite brands all in one place at CouponLink." />
    <meta name="author" content="CouponLink" />
    
    <!-- SEO indexing instructions -->
    <meta name="robots" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
    <meta name="googlebot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
    <meta name="bingbot" content="index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1" />
    
    <!-- Enhanced SEO metadata -->
    <link rel="alternate" hreflang="en" href="https://www.couponlink.in/" />
    <meta name="format-detection" content="telephone=no" />
    <meta property="article:modified_time" content="2023-10-01T12:00:00+00:00" />
    
    <!-- Enhanced favicon setup for better visibility across all platforms -->
    <link rel="icon" href="/favicon/favicon.ico" type="image/x-icon" />
    <link rel="shortcut icon" href="/favicon/favicon.ico" type="image/x-icon" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-touch-icon.png" />
    <link rel="manifest" href="/favicon/site.webmanifest" crossorigin="use-credentials" />
    <meta name="msapplication-TileColor" content="#0095F6" />
    <meta name="theme-color" content="#ffffff" />

    <!-- Open Graph / Facebook -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://www.couponlink.in/" />
    <meta property="og:title" content="CouponLink - Influencer Coupon Management Platform" />
    <meta property="og:description" content="Professional platform for creators to manage and monetize promotional content with a customizable interface." />
    <meta property="og:image" content="/logos/logo.png" />

    <!-- Twitter -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:url" content="https://www.couponlink.in/" />
    <meta name="twitter:title" content="CouponLink - Influencer Coupon Management Platform" />
    <meta name="twitter:description" content="Professional platform for creators to manage and monetize promotional content with a customizable interface." />
    <meta name="twitter:image" content="/logos/logo.png" />
    <meta name="twitter:creator" content="@CouponLink" />

    <!-- Additional SEO -->
    <meta name="keywords" content="influencer marketing, coupon management, promotional codes, creator monetization, deal management" />
    <meta name="application-name" content="CouponLink" />
    
    <!-- Preload critical resources -->
    <script src="/preload.js" defer></script>

    <!-- Clean minimalist loading styles -->
    <style>
      #initial-loader {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: flex;
        justify-content: center;
        align-items: center;
        background: white;
        z-index: 9999;
        font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      }

      .loader-container {
        text-align: center;
      }

      .loader-logo {
        font-size: 2rem;
        font-weight: 600;
        margin-bottom: 2rem;
        color: #0ea76b;
      }

      .loader-spinner {
        width: 40px;
        height: 40px;
        border: 3px solid #f3f4f6;
        border-top: 3px solid #0ea76b;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 1.5rem;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      .loader-text {
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
      }
    </style>
  </head>

  <body>
    <!-- The React app root -->
    <div id="root">
      <!-- Clean loading screen -->
      <div id="initial-loader">
        <div class="loader-container">
          <div class="loader-logo">CouponLink</div>
          <div class="loader-spinner"></div>
          <div class="loader-text">Loading please wait...</div>
        </div>
      </div>
    </div>
    
    <noscript>
      <div style="font-family: sans-serif; color: #721c24; background: #f8d7da; padding: 20px; margin: 20px; border-radius: 5px;">
        <h2>JavaScript Required</h2>
        <p>This application requires JavaScript to run. Please enable JavaScript in your browser settings and reload the page.</p>
      </div>
    </noscript>
    
    <!-- Entry point for the app - Vite will automatically inject the proper script tags here -->
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
