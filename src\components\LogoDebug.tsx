import React, { useState, useEffect, useRef } from 'react';

const LogoDebug: React.FC = () => {
  const [logoStatus, setLogoStatus] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');
  const [imageInfo, setImageInfo] = useState<{width: number, height: number, naturalWidth: number, naturalHeight: number} | null>(null);
  const imgRef = useRef<HTMLImageElement>(null);

  useEffect(() => {
    const img = new Image();

    img.onload = () => {
      console.log('✅ Logo loaded successfully');
      console.log('Image dimensions:', img.width, 'x', img.height);
      console.log('Natural dimensions:', img.naturalWidth, 'x', img.naturalHeight);
      setLogoStatus('loaded');
      setImageInfo({
        width: img.width,
        height: img.height,
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight
      });
    };

    img.onerror = (error) => {
      console.error('❌ Logo failed to load:', error);
      setLogoStatus('error');
      setErrorMessage('Failed to load logo from /logos/logo.png');
    };

    img.src = '/logos/logo.png';
  }, []);

  const handleImageLoad = () => {
    if (imgRef.current) {
      const img = imgRef.current;
      console.log('Direct img element loaded with dimensions:', img.width, 'x', img.height);
      console.log('Natural dimensions:', img.naturalWidth, 'x', img.naturalHeight);
      setImageInfo({
        width: img.width,
        height: img.height,
        naturalWidth: img.naturalWidth,
        naturalHeight: img.naturalHeight
      });
    }
  };

  return (
    <div className="fixed top-4 left-4 z-50 bg-white p-4 rounded-lg shadow-lg border max-w-sm">
      <h3 className="font-bold mb-2">Logo Debug Info</h3>
      
      <div className="mb-3">
        <p className="text-sm">
          Status: <span className={`font-medium ${
            logoStatus === 'loaded' ? 'text-green-600' :
            logoStatus === 'error' ? 'text-red-600' :
            'text-yellow-600'
          }`}>
            {logoStatus}
          </span>
        </p>
        
        {errorMessage && (
          <p className="text-xs text-red-600 mt-1">{errorMessage}</p>
        )}
      </div>

      {imageInfo && (
        <div className="mb-3 text-xs">
          <p className="font-medium">Image Info:</p>
          <p>Display: {imageInfo.width}x{imageInfo.height}</p>
          <p>Natural: {imageInfo.naturalWidth}x{imageInfo.naturalHeight}</p>
        </div>
      )}

      <div className="mb-3">
        <p className="text-xs text-gray-600 mb-1">Direct logo test:</p>
        <div className="w-12 h-12 bg-gray-100 rounded border flex items-center justify-center relative">
          <img
            ref={imgRef}
            src="/logos/logo.png"
            alt="Logo test"
            className="w-full h-full object-contain"
            onLoad={handleImageLoad}
            onError={() => console.log('Direct img element failed')}
          />
          {/* Add a border to see the actual image bounds */}
          <div className="absolute inset-0 border border-red-200 pointer-events-none"></div>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-xs text-gray-600 mb-1">Different backgrounds test:</p>
        <div className="grid grid-cols-4 gap-2">
          <div className="w-8 h-8 bg-white border rounded flex items-center justify-center">
            <img src="/logos/logo.png" alt="White bg" className="w-full h-full object-contain" />
          </div>
          <div className="w-8 h-8 bg-black border rounded flex items-center justify-center">
            <img src="/logos/logo.png" alt="Black bg" className="w-full h-full object-contain" />
          </div>
          <div className="w-8 h-8 bg-blue-500 border rounded flex items-center justify-center">
            <img src="/logos/logo.png" alt="Blue bg" className="w-full h-full object-contain" />
          </div>
          <div className="w-8 h-8 bg-red-500 border rounded flex items-center justify-center">
            <img src="/logos/logo-fallback.svg" alt="SVG fallback" className="w-full h-full object-contain" />
          </div>
        </div>
      </div>

      <div className="mb-3">
        <p className="text-xs text-gray-600 mb-1">Size test (no object-contain):</p>
        <div className="w-16 h-16 bg-gray-100 border rounded relative">
          <img
            src="/logos/logo.png"
            alt="Size test"
            className="absolute inset-0"
            style={{ width: 'auto', height: 'auto', maxWidth: '100%', maxHeight: '100%' }}
          />
        </div>
      </div>

      <div className="text-xs text-gray-500">
        <p>Path: /logos/logo.png</p>
        <p>Base URL: {window.location.origin}</p>
        <p>Full URL: {window.location.origin}/logos/logo.png</p>
      </div>
    </div>
  );
};

export default LogoDebug;
