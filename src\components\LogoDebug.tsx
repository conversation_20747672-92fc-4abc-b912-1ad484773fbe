import React, { useState, useEffect } from 'react';

const LogoDebug: React.FC = () => {
  const [logoStatus, setLogoStatus] = useState<'loading' | 'loaded' | 'error'>('loading');
  const [errorMessage, setErrorMessage] = useState<string>('');

  useEffect(() => {
    const img = new Image();
    
    img.onload = () => {
      console.log('✅ Logo loaded successfully');
      setLogoStatus('loaded');
    };
    
    img.onerror = (error) => {
      console.error('❌ Logo failed to load:', error);
      setLogoStatus('error');
      setErrorMessage('Failed to load logo from /logos/logo.png');
    };
    
    img.src = '/logos/logo.png';
  }, []);

  return (
    <div className="fixed top-4 left-4 z-50 bg-white p-4 rounded-lg shadow-lg border max-w-sm">
      <h3 className="font-bold mb-2">Logo Debug Info</h3>
      
      <div className="mb-3">
        <p className="text-sm">
          Status: <span className={`font-medium ${
            logoStatus === 'loaded' ? 'text-green-600' :
            logoStatus === 'error' ? 'text-red-600' :
            'text-yellow-600'
          }`}>
            {logoStatus}
          </span>
        </p>
        
        {errorMessage && (
          <p className="text-xs text-red-600 mt-1">{errorMessage}</p>
        )}
      </div>

      <div className="mb-3">
        <p className="text-xs text-gray-600 mb-1">Direct logo test:</p>
        <div className="w-12 h-12 bg-gray-100 rounded border flex items-center justify-center">
          <img 
            src="/logos/logo.png" 
            alt="Logo test" 
            className="w-full h-full object-contain"
            onLoad={() => console.log('Direct img element loaded')}
            onError={() => console.log('Direct img element failed')}
          />
        </div>
      </div>

      <div className="text-xs text-gray-500">
        <p>Path: /logos/logo.png</p>
        <p>Base URL: {window.location.origin}</p>
        <p>Full URL: {window.location.origin}/logos/logo.png</p>
      </div>
    </div>
  );
};

export default LogoDebug;
