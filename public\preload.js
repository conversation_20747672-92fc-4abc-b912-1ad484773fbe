// Preload critical resources for faster startup
(function() {
  'use strict';
  
  // Preload critical CSS
  const criticalCSS = [
    '/src/index.css'
  ];
  
  // Preload critical JS modules
  const criticalJS = [
    '/src/main.tsx',
    '/src/App.tsx'
  ];
  
  // Function to create preload link
  function preloadResource(href, as, type) {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = href;
    link.as = as;
    if (type) link.type = type;
    document.head.appendChild(link);
  }
  
  // Preload CSS files
  criticalCSS.forEach(css => {
    preloadResource(css, 'style', 'text/css');
  });
  
  // Preload JS modules
  criticalJS.forEach(js => {
    preloadResource(js, 'script', 'text/javascript');
  });
  
  // Optimize font loading
  const fontPreloads = [
    'https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap'
  ];
  
  fontPreloads.forEach(font => {
    preloadResource(font, 'style', 'text/css');
  });
  
  console.log('Critical resources preloaded');
})();
