import React, { useState, useCallback } from 'react';

interface BrandLogoProps {
  brandName: string;
  logoUrl?: string;
  website?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
  showFallbackText?: boolean;
}

/**
 * BrandLogo component with intelligent fallback logic:
 * 1. Try actual brand logo (if provided)
 * 2. Try Clearbit logo (if website available)
 * 3. Try Logo.dev API
 * 4. Fall back to UI-Avatars
 * 5. Finally show text fallback
 */
const BrandLogo: React.FC<BrandLogoProps> = ({
  brandName,
  logoUrl,
  website,
  size = 'md',
  className = '',
  showFallbackText = true,
}) => {
  const [currentSrc, setCurrentSrc] = useState<string>('');
  const [fallbackLevel, setFallbackLevel] = useState(0);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [showText, setShowText] = useState(false);
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);

  // Size configurations
  const sizeConfig = {
    xs: { width: 'w-6', height: 'h-6', text: 'text-xs' },
    sm: { width: 'w-8', height: 'h-8', text: 'text-sm' },
    md: { width: 'w-12', height: 'h-12', text: 'text-base' },
    lg: { width: 'w-16', height: 'h-16', text: 'text-lg' },
    xl: { width: 'w-20', height: 'h-20', text: 'text-xl' },
  };

  const { width, height, text } = sizeConfig[size];

  // Extract domain from website URL
  const extractDomain = (url: string): string => {
    try {
      const domain = new URL(url.startsWith('http') ? url : `https://${url}`).hostname;
      return domain.replace('www.', '');
    } catch {
      return '';
    }
  };

  // Get logo sources in order of preference
  const getLogoSources = useCallback((): string[] => {
    const sources: string[] = [];

    // 1. Original logo URL (if provided and not a generated one)
    if (logoUrl &&
        !logoUrl.includes('ui-avatars.com') &&
        !logoUrl.includes('logo.clearbit.com') &&
        !logoUrl.includes('google.com/s2/favicons')) {
      sources.push(logoUrl);
    }

    // 1.5. If this is CouponLink, try our fallback SVG
    if (brandName.toLowerCase().includes('couponlink') || brandName.toLowerCase().includes('promocoupons')) {
      sources.push('/logos/logo-fallback.svg');
    }

    // 2. Try domain-based logos if website is available
    if (website) {
      const domain = extractDomain(website);
      if (domain) {
        // Google favicons (most reliable)
        sources.push(`https://www.google.com/s2/favicons?domain=${domain}&sz=128`);

        // Clearbit (good quality but may fail)
        sources.push(`https://logo.clearbit.com/${domain}`);

        // DuckDuckGo icons (alternative)
        sources.push(`https://icons.duckduckgo.com/ip3/${domain}.ico`);
      }
    }

    // 3. UI-Avatars as reliable fallback (always works)
    const uiAvatarUrl = `https://ui-avatars.com/api/?name=${encodeURIComponent(brandName)}&background=6366f1&color=fff&size=128&bold=true&format=png`;
    sources.push(uiAvatarUrl);

    console.log(`🔍 Logo sources for ${brandName}:`, sources);

    return sources;
  }, [logoUrl, website, brandName]);

  // Initialize the first source
  React.useEffect(() => {
    const sources = getLogoSources();
    console.log(`🔍 Initializing logo for ${brandName} with ${sources.length} sources`);

    // Clear any existing timeout
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
    }

    if (sources.length > 0) {
      setCurrentSrc(sources[0]);
      setFallbackLevel(0);
      setImageLoaded(false);
      setShowText(false);

      // Set a timeout to fallback if image takes too long to load
      const timeout = setTimeout(() => {
        console.warn(`⏰ Logo loading timeout for ${brandName}, trying next source`);
        handleImageError();
      }, 5000); // 5 second timeout

      setLoadingTimeout(timeout);
    } else {
      console.warn(`⚠️ No logo sources available for ${brandName}`);
      setShowText(showFallbackText);
    }
  }, [getLogoSources, brandName, showFallbackText]);

  // Handle image load success
  const handleImageLoad = useCallback((event: React.SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget;
    console.log(`✅ Logo loaded successfully for ${brandName}:`, currentSrc);
    console.log(`Image dimensions: ${img.naturalWidth}x${img.naturalHeight}`);

    // Check if image is too small or potentially invisible
    if (img.naturalWidth < 10 || img.naturalHeight < 10) {
      console.warn(`⚠️ Logo for ${brandName} is very small (${img.naturalWidth}x${img.naturalHeight}), trying next source`);
      handleImageError();
      return;
    }

    // Clear timeout on successful load
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }

    setImageLoaded(true);
    setShowText(false);
  }, [brandName, currentSrc, loadingTimeout]);

  // Handle image load error - try next fallback
  const handleImageError = useCallback(() => {
    const sources = getLogoSources();
    const nextLevel = fallbackLevel + 1;

    console.log(`❌ Logo failed to load for ${brandName} (attempt ${fallbackLevel + 1}):`, currentSrc);

    // Clear timeout on error
    if (loadingTimeout) {
      clearTimeout(loadingTimeout);
      setLoadingTimeout(null);
    }

    if (nextLevel < sources.length) {
      console.log(`🔄 Trying next fallback for ${brandName}:`, sources[nextLevel]);
      setFallbackLevel(nextLevel);
      setCurrentSrc(sources[nextLevel]);
      setImageLoaded(false);

      // Set new timeout for next attempt
      const timeout = setTimeout(() => {
        console.warn(`⏰ Logo loading timeout for ${brandName} (attempt ${nextLevel + 1}), trying next source`);
        handleImageError();
      }, 5000);

      setLoadingTimeout(timeout);
    } else {
      // All image sources failed, show text fallback
      console.log(`💬 All logo sources failed for ${brandName}, showing text fallback`);
      setImageLoaded(false);
      setShowText(showFallbackText);
    }
  }, [fallbackLevel, getLogoSources, showFallbackText, brandName, currentSrc, loadingTimeout]);

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
    };
  }, [loadingTimeout]);

  // Get brand initials for text fallback
  const getBrandInitials = (name: string): string => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <div className={`${width} ${height} rounded-lg flex items-center justify-center bg-gray-100 overflow-hidden relative ${className}`}>
      {showText ? (
        // Text fallback
        <div className={`${text} font-bold text-gray-600 text-center`}>
          {getBrandInitials(brandName)}
        </div>
      ) : (
        // Image
        <img
          src={currentSrc}
          alt={`${brandName} logo`}
          className={`w-full h-full object-contain transition-opacity duration-300 ${
            imageLoaded ? 'opacity-100' : 'opacity-0'
          }`}
          onLoad={handleImageLoad}
          onError={handleImageError}
          loading="lazy"
        />
      )}

      {/* Loading state */}
      {!imageLoaded && !showText && currentSrc && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
          <div className="animate-pulse bg-gray-200 rounded w-3/4 h-3/4"></div>
        </div>
      )}
    </div>
  );
};

export default BrandLogo;
