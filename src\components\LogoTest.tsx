import React, { useState } from 'react';
import <PERSON><PERSON><PERSON> from './BrandLogo';

const LogoTest: React.FC = () => {
  const [imageStatus, setImageStatus] = useState<{[key: string]: 'loading' | 'loaded' | 'error'}>({});

  const testImages = [
    { name: 'CouponLink Logo', src: '/logos/logo.png' },
    { name: 'Amazon (Google Favicon)', src: 'https://www.google.com/s2/favicons?domain=amazon.com&sz=128' },
    { name: 'Amazon (Clearbit)', src: 'https://logo.clearbit.com/amazon.com' },
    { name: 'Google (DuckDuckGo)', src: 'https://icons.duckduckgo.com/ip3/google.com.ico' },
    { name: 'UI Avatar', src: 'https://ui-avatars.com/api/?name=Test&background=6366f1&color=fff&size=128&bold=true&format=png' },
  ];

  const handleImageLoad = (name: string) => {
    console.log(`✅ ${name} loaded successfully`);
    setImageStatus(prev => ({ ...prev, [name]: 'loaded' }));
  };

  const handleImageError = (name: string, src: string) => {
    console.log(`❌ ${name} failed to load:`, src);
    setImageStatus(prev => ({ ...prev, [name]: 'error' }));
  };

  const handleImageStart = (name: string) => {
    console.log(`🔄 ${name} started loading`);
    setImageStatus(prev => ({ ...prev, [name]: 'loading' }));
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-6 text-center">Logo Loading Test</h2>
      
      <div className="grid grid-cols-2 gap-4">
        {testImages.map((image) => (
          <div key={image.name} className="border rounded-lg p-4">
            <h3 className="font-semibold mb-2 text-sm">{image.name}</h3>
            
            <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mb-2 relative">
              <img
                src={image.src}
                alt={image.name}
                className="w-full h-full object-contain"
                onLoadStart={() => handleImageStart(image.name)}
                onLoad={() => handleImageLoad(image.name)}
                onError={() => handleImageError(image.name, image.src)}
              />
              
              {/* Status indicator */}
              <div className={`absolute -top-1 -right-1 w-3 h-3 rounded-full ${
                imageStatus[image.name] === 'loaded' ? 'bg-green-500' :
                imageStatus[image.name] === 'error' ? 'bg-red-500' :
                'bg-yellow-500'
              }`} />
            </div>
            
            <div className="text-xs">
              <div className={`font-medium ${
                imageStatus[image.name] === 'loaded' ? 'text-green-600' :
                imageStatus[image.name] === 'error' ? 'text-red-600' :
                'text-yellow-600'
              }`}>
                Status: {imageStatus[image.name] || 'loading'}
              </div>
              <div className="text-gray-500 break-all mt-1">
                {image.src}
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold mb-2">BrandLogo Component Test:</h4>
        <div className="flex gap-4 items-center">
          <div className="text-center">
            <BrandLogo
              brandName="Amazon"
              website="https://amazon.com"
              size="lg"
            />
            <p className="text-xs mt-1">Amazon</p>
          </div>
          <div className="text-center">
            <BrandLogo
              brandName="Google"
              website="https://google.com"
              size="lg"
            />
            <p className="text-xs mt-1">Google</p>
          </div>
          <div className="text-center">
            <BrandLogo
              brandName="Test Company"
              website="https://nonexistent-domain-12345.com"
              size="lg"
            />
            <p className="text-xs mt-1">Fallback Test</p>
          </div>
        </div>
      </div>

      <div className="mt-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-semibold mb-2">Console Output:</h4>
        <p className="text-sm text-gray-600">
          Check the browser console (F12) for detailed loading information.
        </p>
      </div>
    </div>
  );
};

export default LogoTest;
