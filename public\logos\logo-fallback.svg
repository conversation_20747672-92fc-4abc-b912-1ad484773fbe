<svg width="128" height="128" viewBox="0 0 128 128" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background circle -->
  <circle cx="64" cy="64" r="60" fill="url(#gradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#8B5CF6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Letter C -->
  <path d="M45 35 C35 35, 25 45, 25 64 C25 83, 35 93, 45 93 L55 93 L55 83 L45 83 C40 83, 35 78, 35 64 C35 50, 40 45, 45 45 L55 45 L55 35 Z" fill="white"/>
  
  <!-- Letter L -->
  <path d="M65 35 L65 83 L95 83 L95 93 L55 93 L55 35 Z" fill="white"/>
  
  <!-- Small coupon icon -->
  <rect x="75" y="45" width="18" height="12" rx="2" fill="white" opacity="0.8"/>
  <circle cx="78" cy="51" r="1" fill="#3B82F6"/>
  <circle cx="90" cy="51" r="1" fill="#3B82F6"/>
  <line x1="81" y1="49" x2="87" y2="49" stroke="#3B82F6" stroke-width="0.5"/>
  <line x1="81" y1="51" x2="87" y2="51" stroke="#3B82F6" stroke-width="0.5"/>
  <line x1="81" y1="53" x2="87" y2="53" stroke="#3B82F6" stroke-width="0.5"/>
</svg>
