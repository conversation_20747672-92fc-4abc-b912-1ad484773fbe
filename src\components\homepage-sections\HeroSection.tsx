import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { FiYoutube, FiInstagram, FiSearch, FiCopy, FiCheck, FiX, FiArrowRight, FiChevronLeft, FiChevronRight, FiClock, FiTrendingUp, FiStar, FiExternalLink, FiShield } from 'react-icons/fi';
import { FaTiktok, FaFire, FaRegLightbulb, FaGift } from 'react-icons/fa';
import { Link, useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { COLORS, PLATFORMS, DEALS } from '@/constants/theme';
import { Platform, CreatorDeal } from '@/types';

// Types
interface HeroSectionProps {
  searchQuery: string;
  setSearchQuery: React.Dispatch<React.SetStateAction<string>>;
  handleSearch: (e: React.FormEvent) => void;
  socialFeedItems?: any[];
  onBrowseDealsClick?: () => void;
}

// Platform selection button - Memoized component
const PlatformButton = React.memo<{ name: string; platform: Platform }>(({ name, platform }) => (
  <motion.div
    whileHover={{ scale: 1.05 }}
    className="platform-button"
    style={{ 
      backgroundColor: platform.bgColor,
      color: platform.color
    }}
  >
    <div 
      className="w-4 h-4 sm:w-5 sm:h-5 rounded-full flex items-center justify-center"
      style={{ 
        background: platform.gradient,
        color: 'white' 
      }}
    >
      {platform.icon}
    </div>
    <span className="text-xs sm:text-sm font-medium">{name}</span>
  </motion.div>
));

PlatformButton.displayName = 'PlatformButton';

// Custom hooks for responsive styles
const usePositionStyles = (position: string, windowWidth: number) => {
  return useMemo(() => {
    const baseStyles = {
      main: {
        zIndex: 40,
        left: '0',
        top: '0',
        opacity: 1,
        scale: 1,
        rotate: 0
      },
      second: {
        zIndex: 30,
        left: '30px',
        top: '20px',
        opacity: 0.9,
        scale: 0.95,
        rotate: 2
      },
      third: {
        zIndex: 20,
        left: '60px',
        top: '40px',
        opacity: 0.8,
        scale: 0.9,
        rotate: 4
      },
      fourth: {
        zIndex: 10,
        left: '90px',
        top: '60px',
        opacity: 0.7,
        scale: 0.85,
        rotate: 6
      }
    };

    // Adjust spacing for larger screens
    if (windowWidth >= 640) {
      baseStyles.second.left = '60px';
      baseStyles.second.top = '40px';
      baseStyles.third.left = '120px';
      baseStyles.third.top = '60px';
      baseStyles.fourth.left = '180px';
      baseStyles.fourth.top = '80px';
    }

    return baseStyles[position] || baseStyles.main;
  }, [position, windowWidth]);
};

const useCardSizes = (position: string, windowWidth: number) => {
  return useMemo(() => {
    let sizes = {
      width: '260px',
      height: '340px'
    };
    
    if (windowWidth >= 640) {
      sizes = {
        width: position === 'main' ? '320px' : '300px',
        height: position === 'main' ? '380px' : '360px'
      };
    }

    return sizes;
  }, [position, windowWidth]);
};

// Deal Card Component - Optimized with memo
const DealCard = React.memo<{
  deal: CreatorDeal;
  position: string;
  handleCopy: (code: string) => void;
  copied: boolean;
  isAnimating: boolean;
}>(({ deal, position, handleCopy, copied, isAnimating }) => {
  const platform = PLATFORMS[deal.platform];
  const [windowWidth, setWindowWidth] = useState(typeof window !== 'undefined' ? window.innerWidth : 1024);
  const navigate = useNavigate();

  useEffect(() => {
    const handleResize = () => setWindowWidth(window.innerWidth);
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const styles = usePositionStyles(position, windowWidth);
  const cardSizes = useCardSizes(position, windowWidth);
  const isInteractive = position === 'main';
  
  return (
    <motion.div
      className={`deal-card ${isInteractive ? '' : 'pointer-events-none'}`}
      animate={styles}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
        duration: 0.6
      }}
      style={{
        position: 'absolute',
        width: cardSizes.width,
        height: cardSizes.height,
        transformOrigin: 'center center',
      }}
    >
      <div 
        className="absolute inset-x-0 top-0 h-1.5"
        style={{ 
          background: platform.gradient,
          opacity: position === 'main' ? 1 : 0.7
        }}
      />
      
      {position !== 'main' && (
        <div
          className="absolute top-2 right-2 px-2 py-0.5 rounded-full text-xs font-medium"
          style={{
            background: position === 'second' ? 
              'linear-gradient(135deg, rgba(99, 102, 241, 0.2) 0%, rgba(79, 70, 229, 0.2) 100%)' : 
              'linear-gradient(135deg, rgba(156, 163, 175, 0.2) 0%, rgba(107, 114, 128, 0.2) 100%)',
            color: position === 'second' ? COLORS.primary.main : COLORS.neutral[600]
          }}
        >
          {position === 'second' ? 'Next' : 'Upcoming'}
        </div>
      )}
      
      <div className="deal-card-header">
        <div className="flex items-center gap-3">
          <img 
            src={deal.avatar} 
            alt={deal.creatorName}
            className="w-8 h-8 sm:w-10 sm:h-10 rounded-full object-cover ring-2 ring-white"
            loading="lazy"
          />
          <div>
            <div className="font-bold text-sm sm:text-base" style={{ color: COLORS.neutral[900] }}>
              {deal.creatorHandle}
            </div>
            <div className="flex items-center gap-1 sm:gap-2 text-xs" style={{ color: COLORS.neutral[500] }}>
              <div className="flex items-center">
                <FiClock className="w-3 h-3 mr-1" />
                <span className="text-xs">Added: {deal.addedAt}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="deal-card-body">
        <div className="flex items-start mb-2 sm:mb-3">
          <div 
            className="mr-2 p-1 sm:p-1.5 rounded"
            style={{ 
              backgroundColor: COLORS.secondary.bgLight,
              color: COLORS.secondary.main 
            }}
          >
            <FaFire className="w-3 h-3 sm:w-4 sm:h-4" />
          </div>
          <div className="font-bold text-base sm:text-lg" style={{ color: COLORS.neutral[900] }}>
            {deal.discount} {deal.description}
          </div>
        </div>
        
        <div 
          className="flex items-center gap-1 sm:gap-2 mb-2 sm:mb-3 text-xs sm:text-sm py-1 px-2 rounded-md self-start"
          style={{ 
            backgroundColor: platform.bgColor,
            color: platform.color
          }}
        >
          {platform.icon}
          <span>Platform: {platform.name}</span>
        </div>
        
        <div className="flex flex-wrap gap-1 sm:gap-1.5 mb-auto">
          {deal.tags.map(tag => (
            <span
              key={tag}
              className="text-xs px-2 py-0.5 rounded-full"
              style={{ 
                backgroundColor: COLORS.neutral[100],
                color: COLORS.neutral[600]
              }}
            >
              #{tag}
            </span>
          ))}
        </div>
        
        {position === 'main' && deal.code && (
          <div className="mt-3 sm:mt-4 grid grid-cols-2 gap-3">
            <motion.button
              onClick={() => handleCopy(deal.code || '')}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className={`deal-copy-button ${copied ? 'bg-[#008bf8]' : ''}`}
            >
              {copied ? <FiCheck className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5" /> : <FiCopy className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5" />}
              {copied ? 'Copied' : 'Copy Code'}
            </motion.button>
            
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="deal-visit-button"
              onClick={() => {
                if (deal.affiliate_link) {
                  navigate(deal.affiliate_link);
                }
              }}
            >
              <FiExternalLink className="w-3 h-3 sm:w-4 sm:h-4 mr-1.5" />
              Visit Deal
            </motion.button>
          </div>
        )}
      </div>

      {position === 'main' && deal.code && (
        <div className="deal-card-footer">
          <div className="bg-white py-1.5 px-2 rounded-md mx-auto inline-block shadow-sm">
            {deal.code}
          </div>
        </div>
      )}
    </motion.div>
  );
});

DealCard.displayName = 'DealCard';

// Main HeroSection component
export const HeroSection: React.FC<HeroSectionProps> = ({
  searchQuery,
  setSearchQuery,
  handleSearch,
  onBrowseDealsClick
}) => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [copied, setCopied] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);
  
  const handleCopy = useCallback((code: string) => {
    navigator.clipboard.writeText(code);
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  }, []);
  
  const ANIMATION_DURATION = 700;
  
  const nextDeal = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setActiveIndex((prev) => (prev + 1) % DEALS.length);
    setTimeout(() => setIsAnimating(false), ANIMATION_DURATION);
  }, [isAnimating]);
  
  const prevDeal = useCallback(() => {
    if (isAnimating) return;
    setIsAnimating(true);
    setActiveIndex((prev) => (prev - 1 + DEALS.length) % DEALS.length);
    setTimeout(() => setIsAnimating(false), ANIMATION_DURATION);
  }, [isAnimating]);
  
  const dealIndexes = useMemo(() => [
    activeIndex,
    (activeIndex + 1) % DEALS.length,
    (activeIndex + 2) % DEALS.length,
    (activeIndex + 3) % DEALS.length
  ], [activeIndex]);
  
  useEffect(() => {
    const AUTO_ROTATION_INTERVAL = 5000;
    if (isAnimating) return;
    
    const rotateTimer = setInterval(nextDeal, AUTO_ROTATION_INTERVAL);
    return () => clearInterval(rotateTimer);
  }, [isAnimating, nextDeal]);

  return (
    <section className="hero-section">
      {/* CouponLink Logo moved to top of the page and left-aligned */}
      <motion.div 
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="flex items-center gap-2 mb-2 pt-2 px-2 sm:px-4 lg:px-6"
      >
        <img
          src="/logos/logo.png"
          alt="CouponLink Logo"
          className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 object-contain"
          loading="eager"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/logos/logo-fallback.svg';
            target.onError = () => {
              target.style.display = 'none';
              const fallback = target.nextElementSibling as HTMLElement;
              if (fallback) fallback.style.display = 'flex';
            };
          }}
        />
        <div
          className="w-8 h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-sm md:text-base lg:text-lg"
          style={{ display: 'none' }}
        >
          CL
        </div>
        <span className="text-2xl md:text-2xl lg:text-3xl font-bold text-black dark:text-white">
          CouponLink
        </span>
      </motion.div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 md:px-8 lg:px-8 py-8 sm:py-12 md:py-16 relative overflow-hidden">
        <div className="grid lg:grid-cols-2 gap-8 lg:gap-12 items-start">
          {/* Left side content */}
          <div className="flex flex-col justify-start order-1">
            <motion.h1
              className="text-4xl md:text-5xl font-extrabold mb-3 leading-tight text-black dark:text-white"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              All your <span className="relative inline-block px-2">
                <span className="bg-gradient-to-r from-[#0ea76b] to-[#0a8554] text-transparent bg-clip-text">coupons</span>
                <span className="absolute left-0 right-0 bottom-0 h-1 bg-[#0ea76b]/20 rounded-full -z-10"></span>
              </span> in one place
            </motion.h1>

            <motion.p
              className="text-base md:text-lg mb-6 text-gray-600 max-w-md"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Instantly discover, save, and use the best creator deals. Minimal effort. Maximum savings.
            </motion.p>

            <motion.div
              className="flex flex-col items-start gap-3 mb-8 md:mb-10"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <motion.button
                className="px-8 py-4 rounded-full bg-gradient-to-r from-[#0ea76b] to-[#0a8554] text-white text-lg font-semibold shadow-lg flex items-center gap-3 hover:scale-105 transition-transform"
                whileHover={{ scale: 1.07 }}
                whileTap={{ scale: 0.97 }}
                onClick={onBrowseDealsClick}
              >
                Browse Deals
                <FiArrowRight className="w-5 h-5" />
              </motion.button>
              <span className="text-xs text-gray-400 mt-1 ml-1">No signup needed</span>
              <div className="flex items-center gap-2 mt-2">
                {Object.entries(PLATFORMS).map(([key, platform]) => (
                  <span key={key} className="w-7 h-7 rounded-full flex items-center justify-center bg-white shadow border border-gray-100">
                    <span className="text-lg" style={{ color: platform.color }}>{platform.icon}</span>
                  </span>
                ))}
              </div>
            </motion.div>

            <motion.div
              className="flex items-center gap-2 text-xs sm:text-sm text-gray-400 mt-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <FiShield className="w-4 h-4" />
              <span>All deals verified from creator accounts</span>
            </motion.div>
          </div>
          
          {/* Right side - Coupon cards */}
          <div className="order-2 w-full mt-8 sm:mt-12 lg:mt-0">
            <motion.div 
              className="relative mx-auto h-[400px] sm:h-[450px] max-w-[320px] sm:max-w-[400px] lg:w-full pt-2 sm:pt-4 pb-20 sm:pb-16"
              initial={{ opacity: 0, scale: 0.95 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7 }}
            >
              
              {/* Live Feed indicator */}
              <div 
                className="absolute -top-10 sm:-top-8 right-[-4rem] lg:[right:-10rem] z-30 px-4 py-1.5 sm:py-2 rounded-full flex items-center gap-2 shadow-lg border border-red-200 bg-white/95 animate-fadeIn"
                style={{ 
                  background: 'rgba(255,255,255,0.98)',
                  backdropFilter: 'blur(14px)',
                  boxShadow: '0 0 16px 2px rgba(255,0,80,0.10)',
                }}
              >
                <div className="w-2.5 h-2.5 rounded-full bg-red-500 animate-pulse shadow-[0_0_8px_2px_rgba(239,68,68,0.5)]" />
                <span className="text-xs sm:text-sm font-bold text-red-600 tracking-wide drop-shadow">Live Feed</span>
              </div>
              
              {/* Cards container */}
              <div className="relative h-full w-full flex items-center justify-center">
                <AnimatePresence>
                  {dealIndexes.map((dealIndex, index) => {
                    const positionMap = ['main', 'second', 'third', 'fourth'];
                    const position = positionMap[index];
                    
                    return (
                      <DealCard
                        key={`${position}-${DEALS[dealIndex].id}`}
                        deal={DEALS[dealIndex]}
                        position={position}
                        handleCopy={handleCopy}
                        copied={copied}
                        isAnimating={isAnimating}
                      />
                    );
                  })}
                </AnimatePresence>
              </div>
            </motion.div>
            {/* Creative bubble stepper for card change */}
            <div className="w-full flex justify-center mt-4">
              <div className="flex items-center gap-4">
                {[0, 1, 2, 3].map((idx) => {
                  const prevIdx = (activeIndex - 1 + 4) % 4;
                  const nextIdx = (activeIndex + 1) % 4;
                  let className = "w-4 h-4 rounded-full border-2 border-gray-200 bg-gray-100";
                  if (idx === activeIndex) {
                    className = "w-5 h-5 rounded-full bg-gradient-to-r from-[#0ea76b] to-[#0a8554] shadow-md border-2 border-[#0ea76b]";
                  } else if (idx === prevIdx) {
                    className = "w-4 h-4 rounded-full border-2 border-[#0ea76b]/60 bg-[#d1fae5]";
                  } else if (idx === nextIdx) {
                    className = "w-4 h-4 rounded-full border-2 border-[#0ea76b]/80 bg-[#b2f5ea]";
                  }
                  return (
                    <motion.div
                      key={idx}
                      className={className}
                      animate={
                        idx === activeIndex
                          ? { scale: [1, 1.25, 1], boxShadow: [
                              "0 0 0 0 rgba(14,167,107,0.3)",
                              "0 0 0 8px rgba(14,167,107,0.1)",
                              "0 0 0 0 rgba(14,167,107,0.3)"
                            ] }
                          : { scale: 1, boxShadow: "none" }
                      }
                      transition={
                        idx === activeIndex
                          ? { duration: 5, repeat: Infinity, ease: "easeInOut" }
                          : { duration: 0.2 }
                      }
                    />
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection; 